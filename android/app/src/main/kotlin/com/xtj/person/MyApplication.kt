package com.xtj.person

import android.os.Bundle
import com.tencent.chat.flutter.push.tencent_cloud_chat_push.application.TencentCloudChatPushApplication
import com.umeng.commonsdk.UMConfigure
import com.umeng.umcrash.UMCrash
import com.xtj.person.common.base.BaseApplication

class MyApplication : BaseApplication(){
    private var loaded = false
    override fun onCreate() {
        super.onCreate()

        val bundle = Bundle()

        bundle.putBoolean(UMCrash.KEY_ENABLE_CRASH_JAVA, true)
        bundle.putBoolean(UMCrash.KEY_ENABLE_CRASH_NATIVE, true)
        bundle.putBoolean(UMCrash.KEY_ENABLE_FLUTTER, true)
        bundle.putBoolean(UMCrash.KEY_ENABLE_ANR, false)
        bundle.putBoolean(UMCrash.KEY_ENABLE_PA, false)
        bundle.putBoolean(UMCrash.KEY_ENABLE_LAUNCH, false)
        bundle.putBoolean(UMCrash.KEY_ENABLE_MEM, false)
        bundle.putBoolean(UMCrash.KEY_ENABLE_H5PAGE, false)
        bundle.putBoolean(UMCrash.KEY_ENABLE_POWER, false)
        UMCrash.initConfig(bundle)

        UMConfigure.preInit(getApplicationContext(), "678da7049a16fe6dcd324c8a", "Android")


        inst = this

        if (loaded) return

        try {
            System.loadLibrary("c++_shared")
        } catch (ignored: Throwable) {
        }
        // Load dependencies first
        // 可选：如果引入了 OpenCV，把 libopencv_java4.so 放到 jniLibs 并尝试加载
        try {
            System.loadLibrary("opencv_java4")
        } catch (ignored: Throwable) {
        }
        try {
            System.loadLibrary("omp")
        } catch (ignored: Throwable) {
        }
        try {
            System.loadLibrary("SeetaAuthorize")
        } catch (ignored: Throwable) {
        }
        try {
            System.loadLibrary("tennis")
        } catch (ignored: Throwable) {
        }
        try {
            System.loadLibrary("SeetaFaceDetector600")
        } catch (ignored: Throwable) {
        }
        try {
            System.loadLibrary("SeetaFaceLandmarker600")
        } catch (ignored: Throwable) {
        }
        try {
            System.loadLibrary("SeetaFaceRecognizer610")
        } catch (ignored: Throwable) {
        }

        System.loadLibrary("native-lib")

        loaded = true
    }

    companion object {
        var inst: MyApplication? = null
    }
}
