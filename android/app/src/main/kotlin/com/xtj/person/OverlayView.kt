package com.xtj.person

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.xtj.person.common.ext.dp2px

class OverlayView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : View(context, attrs) {

    private var paintColor = Color.GREEN
    private val textMargin = dp2px(10f) // 文本与边界的间距

    private val boxPaint = Paint().apply {
        color = paintColor
        style = Paint.Style.STROKE
        strokeWidth = 4f
    }

    private val textPaint = Paint().apply {
        color = paintColor
        textSize = 36f
        textAlign = Paint.Align.RIGHT // 设置文本右对齐
    }

    @Volatile private var boxes: IntArray = intArrayOf()
    @Volatile private var labels: Array<String> = arrayOf()

    fun setData(b: IntArray, l: Array<String>, paintColor: Int) {
        boxes = b
        labels = l
        boxPaint.color = paintColor
        textPaint.color = paintColor
        postInvalidateOnAnimation()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val b = boxes
        for (i in 0 until (b.size/4)) {
            val x = b[i*4]
            val y = b[i*4+1]
            val w = b[i*4+2]
            val h = b[i*4+3]

            // 计算角线长度（取宽度的五分之一）
            val cornerLength = w / 5f

            // 绘制左上角
            canvas.drawLine(x.toFloat(), y.toFloat(), x + cornerLength, y.toFloat(), boxPaint)
            canvas.drawLine(x.toFloat(), y.toFloat(), x.toFloat(), y + cornerLength, boxPaint)

            // 绘制右上角
            canvas.drawLine((x + w - cornerLength), y.toFloat(), (x + w).toFloat(), y.toFloat(), boxPaint)
            canvas.drawLine((x + w).toFloat(), y.toFloat(), (x + w).toFloat(), y + cornerLength, boxPaint)

            // 绘制左下角
            canvas.drawLine(x.toFloat(), (y + h).toFloat(), x + cornerLength, (y + h).toFloat(), boxPaint)
            canvas.drawLine(x.toFloat(), (y + h - cornerLength), x.toFloat(), (y + h).toFloat(), boxPaint)

            // 绘制右下角
            canvas.drawLine((x + w - cornerLength), (y + h).toFloat(), (x + w).toFloat(), (y + h).toFloat(), boxPaint)
            canvas.drawLine((x + w).toFloat(), (y + h - cornerLength), (x + w).toFloat(), (y + h).toFloat(), boxPaint)

            val label = if (i < labels.size) labels[i] else ""
            // 支持多行标签：按 \n 分行绘制
            val lines = label.split('\n')

            // 计算文本绘制位置（右下角内，与右下角对齐并保留5px间距）
            val textRight = (x + w).toFloat() - textMargin // 文本右边界与框右边界对齐（减去5px间距）
            var textBottom = (y + h).toFloat() - textMargin // 文本底部与框底部对齐（减去5px间距）

            // 从下往上绘制多行文本
            for (line in lines.reversed()) {
                canvas.drawText(line, textRight, textBottom, textPaint)
                textBottom -= (textPaint.textSize + 6f)
            }
        }
    }
}