package com.xtj.person

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Rational
import android.graphics.Matrix
import android.graphics.RectF
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.core.ViewPort
import androidx.camera.core.UseCaseGroup
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.camera.view.PreviewView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import java.util.concurrent.Executors
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import android.graphics.Bitmap
import android.graphics.ImageFormat
import android.graphics.Rect
import android.widget.ImageView
import androidx.annotation.OptIn
import androidx.camera.core.ImageProxy
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.common.ext.getColorExt
import com.xtj.person.common.util.JniUtils.detectNV21
import com.xtj.person.common.util.JniUtils.extractNV21
import com.xtj.person.common.util.JniUtils.seetaSimilarity
import dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter



class CameraActivity : AppCompatActivity() {




    data class Entry(val name: String, val feat: FloatArray)
    private val db = mutableListOf<Entry>()
    private var lastNV21: ByteArray? = null
    private var frameW: Int = 0
    private var frameH: Int = 0
    private var frameRotation: Int = 0

    private lateinit var previewView: PreviewView
    private lateinit var overlay: OverlayView
    private lateinit var nameEdit: EditText
    private lateinit var ivFace: ImageView
    private val ENABLE_RECOG = true // 启用识别功能

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_camera)
        previewView = findViewById(R.id.preview)
        overlay = findViewById(R.id.overlay)
        nameEdit = findViewById(R.id.nameEdit)
        ivFace = findViewById<ImageView>(R.id.iv_face)
        findViewById<Button>(R.id.btnRegister).setOnClickListener { registerCurrent() }
        findViewById<Button>(R.id.btnClear).setOnClickListener { db.clear(); Toast.makeText(this, "已清空库", Toast.LENGTH_SHORT).show() }
        // Cache view size and scaleType on main thread to avoid touching PreviewView from analyzer thread
        cachedScaleType = previewView.scaleType
        previewView.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            viewW = v.width
            viewH = v.height
        }
        ensurePermissionsAndStart()
    }

    private fun applyFullscreen() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        val controller = WindowInsetsControllerCompat(window, window.decorView)
        controller.hide(WindowInsetsCompat.Type.statusBars() or WindowInsetsCompat.Type.navigationBars())
        controller.systemBarsBehavior =
            WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) applyFullscreen()
    }


    private fun ensurePermissionsAndStart() {
        val perms = arrayOf(Manifest.permission.CAMERA)
        val need = perms.any { ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED }
        if (need) ActivityCompat.requestPermissions(this, perms, 100)
        bindCamera()
    }

    private var isFrontCamera: Boolean = true
    @Volatile private var cachedScaleType: PreviewView.ScaleType = PreviewView.ScaleType.FILL_START
    @Volatile private var viewW: Int = 0
    @Volatile private var viewH: Int = 0


    private var cameraProvider: ProcessCameraProvider? = null
    private var cameraExecutor = Executors.newSingleThreadExecutor()
    private var camera: Camera? = null

    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
        cameraProvider?.unbindAll()
    }


    private fun bindCamera() {
        val providerFuture = ProcessCameraProvider.getInstance(this)
        providerFuture.addListener({
            val provider = providerFuture.get()
            // Choose aspect ratio closest to screen for larger FOV
            val dm = resources.displayMetrics
            val screenW = dm.widthPixels
            val screenH = dm.heightPixels
            val ratio = chooseAspectRatio(screenW, screenH)

            val preview = Preview.Builder()
                .setTargetAspectRatio(ratio)
                .setTargetRotation(previewView.display.rotation)
                .build()
            preview.setSurfaceProvider(previewView.surfaceProvider)

            // Fill the view to mimic native camera preview experience
            previewView.scaleType = PreviewView.ScaleType.FILL_CENTER
            // Update cached scale type after setting
            cachedScaleType = previewView.scaleType

            val analyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setTargetAspectRatio(ratio)
                .setTargetRotation(previewView.display.rotation)
                .build()
             cameraExecutor = Executors.newSingleThreadExecutor()
            analyzer.setAnalyzer(cameraExecutor) { image ->
                analyzeFrame(image)
                image.close()
            }

            val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA
            isFrontCamera = (cameraSelector == CameraSelector.DEFAULT_FRONT_CAMERA)
            try {
                provider.unbindAll()
                // Use a shared ViewPort so preview and analysis have consistent crop/FOV
                val vpW = if (viewW > 0) viewW else screenW
                val vpH = if (viewH > 0) viewH else screenH
                val rotation = previewView.display.rotation
                val viewPort = ViewPort.Builder(Rational(vpW, vpH), rotation)
                    .setScaleType(ViewPort.FILL_CENTER)
                    .setLayoutDirection(previewView.layoutDirection)
                    .build()
                val group = UseCaseGroup.Builder()
                    .addUseCase(preview)
                    .addUseCase(analyzer)
                    .setViewPort(viewPort)
                    .build()
                camera = provider.bindToLifecycle(this as LifecycleOwner, cameraSelector, group)
            } catch (_: Exception) {}
        }, ContextCompat.getMainExecutor(this))
    }

    private fun analyzeFrame(image: ImageProxy) {
        val nv21 = yuv420888ToNV21(image)
        frameW = image.width
        frameH = image.height
        frameRotation = image.imageInfo.rotationDegrees
        lastNV21 = nv21

        // Debug: Log actual dimensions (use cached view size; don't touch View off main thread)
        val previewW = viewW
        val previewH = viewH
        Log.d("DEBUG", "Frame: ${frameW}x${frameH}, Preview: ${previewW}x${previewH}, Rotation: ${frameRotation}")

        val boxes = detectNV21(nv21, frameW, frameH, frameRotation,this@CameraActivity)
        val labels = Array(boxes.size/4) { i -> "face_${i}" }
        if (ENABLE_RECOG) {
            // recognize best match for each box
            for (i in 0 until (boxes.size/4)) {
                val x=boxes[i*4]; val y=boxes[i*4+1]; val w=boxes[i*4+2]; val h=boxes[i*4+3]
                val feat = extractNV21(nv21, frameW, frameH, x,y,w,h, frameRotation,this@CameraActivity)
                //显示人脸图片




                if (feat.isNotEmpty() && db.isNotEmpty()) {
                    var bestCos = Float.NEGATIVE_INFINITY
                    var bestName = "unknown"
                    var bestSeeta = Float.NEGATIVE_INFINITY
                    var bestEntry: Entry? = null
                    db.forEach { e ->
                        val s = cosine(feat, e.feat)
                        if (s > bestCos) {
                            bestCos = s
                            bestName = e.name
                            // Compare Seeta's built-in similarity for the same pair
                            bestSeeta = try { seetaSimilarity(feat, e.feat,this@CameraActivity) } catch (_: Throwable) { Float.NaN }
                            bestEntry = e
                        }
                    }
                    val cosStr = String.format("%.2f", bestCos)
                    val seetaStr = if (bestSeeta.isNaN()) "NaN" else String.format("%.2f", bestSeeta)
//                    labels[i] = if (bestCos > 0.7f) {
//                        "$bestName(cos=$cosStr, seeta=$seetaStr)"
//                    } else {
//                        "cos=$cosStr, seeta=$seetaStr"
//                    }
                   if (bestCos > 0.7f) {
                       ToastUtils.showLong(bestName.toString()+"签到成功")
                        showFaceBitmap(image,boxes,i,feat)
                       labels[i] =   "$bestName 相似度:${cosStr.toFloat()*100}%"
                       db.remove(bestEntry)
                    }
                }
            }
        }

        // Debug: Log original boxes
        for (i in 0 until (boxes.size/4)) {
            Log.d("DEBUG", "Original box $i: (${boxes[i*4]}, ${boxes[i*4+1]}, ${boxes[i*4+2]}, ${boxes[i*4+3]})")
        }

        // Transform coordinates from image analysis to preview display
        val transformedBoxes = transformCoordinates(boxes)

        // Debug: Log transformed boxes
        for (i in 0 until (transformedBoxes.size/4)) {
            Log.d("DEBUG", "Transformed box $i: (${transformedBoxes[i*4]}, ${transformedBoxes[i*4+1]}, ${transformedBoxes[i*4+2]}, ${transformedBoxes[i*4+3]})")
        }

        if (viewW > 0 && viewH > 0) {
            runOnUiThread { overlay.setData(transformedBoxes, labels, getColorExt(R.color.redFF0000Alpha80)) }
        }
    }

    fun showFaceBitmap(image: ImageProxy,boxes: IntArray,boxesIndex:Int,feat: FloatArray){
        imageProxyToBitmap(image)?.let { bitmap ->
            val rotatedBitmap = rotateBitmap(bitmap, image.imageInfo.rotationDegrees)
                val expandedRect = expandCropArea(
                    boxes[boxesIndex*4], boxes[boxesIndex*4+1], boxes[boxesIndex*4+2], boxes[boxesIndex*4+3],
                    scale = 0.2f, // 扩大20%
                    bitmapWidth = rotatedBitmap.width,
                    bitmapHeight = rotatedBitmap.height
                )
                cropBitmap(rotatedBitmap,  expandedRect.left,
                    expandedRect.top,
                    expandedRect.width(),
                    expandedRect.height())?.let { cropBitmap ->
                  val mirrorBitMap =  mirrorBitmap(cropBitmap)
                    runOnUiThread {
                        ivFace.setImageBitmap(mirrorBitMap)
                    }
                }
            rotatedBitmap.recycle() // 释放原始 Bitmap
        }
    }

    fun expandCropArea(
        x: Int, y: Int, width: Int, height: Int,
        scale: Float,
        bitmapWidth: Int, bitmapHeight: Int
    ): Rect {
        val expandX = (width * scale / 2).toInt()
        val expandY = (height * scale / 2).toInt()

        val newX = (x - expandX).coerceAtLeast(0)
        val newY = (y - expandY).coerceAtLeast(0)
        val newWidth = (width + 2 * expandX).coerceAtMost(bitmapWidth - newX)
        val newHeight = (height + 2 * expandY).coerceAtMost(bitmapHeight - newY)

        return Rect(newX, newY, newX + newWidth, newY + newHeight)
    }

    fun cropBitmap(bitmap: Bitmap, x: Int, y: Int, width: Int, height: Int): Bitmap? {
        if (!isCropValid(bitmap, x, y, width, height)) {
            return null // 非法区域，返回 null 或抛出异常
        }
        return Bitmap.createBitmap(bitmap, x, y, width, height)
    }

    fun isCropValid(bitmap: Bitmap, x: Int, y: Int, width: Int, height: Int): Boolean {
        return x >= 0 && y >= 0 &&
                width > 0 && height > 0 &&
                x + width <= bitmap.width &&
                y + height <= bitmap.height
    }


    /**
     * 水平镜像翻转 Bitmap
     * @param bitmap 原始 Bitmap
     * @return 翻转后的 Bitmap（新对象）
     */
    fun mirrorBitmap(bitmap: Bitmap): Bitmap {
        // 创建 Matrix 并设置水平翻转（-1 表示镜像）
        val matrix = Matrix().apply {
            postScale(-1f, 1f) // 水平翻转（x轴取反，y轴不变）
        }

        // 创建翻转后的 Bitmap
        return Bitmap.createBitmap(
            bitmap,          // 原始 Bitmap
            0,               // 起始 x 坐标
            0,               // 起始 y 坐标
            bitmap.width,    // 宽度
            bitmap.height,   // 高度
            matrix,          // 应用变换矩阵
            true             // 是否抗锯齿
        )
    }



    fun rotateBitmap(bitmap: Bitmap, rotationDegrees: Int): Bitmap {
        if (rotationDegrees == 0) return bitmap

        val matrix = Matrix().apply {
            postRotate(rotationDegrees.toFloat())
        }

        return Bitmap.createBitmap(
            bitmap,
            0,
            0,
            bitmap.width,
            bitmap.height,
            matrix,
            true
        )
    }


    @OptIn(ExperimentalGetImage::class)
    fun imageProxyToBitmap(imageProxy: ImageProxy): Bitmap? {
        if (imageProxy.format != ImageFormat.YUV_420_888) {
            return null // 仅处理 YUV 格式
        }

        val bitmap = Bitmap.createBitmap(
            imageProxy.width,
            imageProxy.height,
            Bitmap.Config.ARGB_8888
        )

        val converter = YuvToRgbConverter(this@CameraActivity)
        converter.yuvToRgb(imageProxy.image!!, bitmap)

        return bitmap
    }
    private fun transformCoordinates(boxes: IntArray): IntArray {
        if (boxes.isEmpty()) return boxes

        val previewWidth = viewW
        val previewHeight = viewH

        if (previewWidth == 0 || previewHeight == 0) return boxes

        // Dimensions of the analysis buffer after rotation applied in native
        val analysisW = if (frameRotation == 90 || frameRotation == 270) frameH else frameW
        val analysisH = if (frameRotation == 90 || frameRotation == 270) frameW else frameH

        // Build a transform matrix once, then map all boxes via 矩阵映射
        val m = buildTransformMatrix(
            analysisW = analysisW,
            analysisH = analysisH,
            viewW = previewWidth,
            viewH = previewHeight,
            scaleType = cachedScaleType,
            mirror = isFrontCamera
        )

        Log.d("DEBUG", "Transform: analysis=${analysisW}x${analysisH}, view=${previewWidth}x${previewHeight}, front=${isFrontCamera}, scaleType=${cachedScaleType}")

        val out = IntArray(boxes.size)
        for (i in 0 until (boxes.size / 4)) {
            val x = boxes[i * 4].toFloat()
            val y = boxes[i * 4 + 1].toFloat()
            val w = boxes[i * 4 + 2].toFloat()
            val h = boxes[i * 4 + 3].toFloat()
            val r = RectF(x, y, x + w, y + h)
            m.mapRect(r)
            out[i * 4] = r.left.toInt()
            out[i * 4 + 1] = r.top.toInt()
            out[i * 4 + 2] = r.width().toInt()
            out[i * 4 + 3] = r.height().toInt()
        }
        return out
    }

    private fun buildTransformMatrix(
        analysisW: Int,
        analysisH: Int,
        viewW: Int,
        viewH: Int,
        scaleType: PreviewView.ScaleType,
        mirror: Boolean
    ): Matrix {
        val scaleForFit = minOf(viewW.toFloat() / analysisW, viewH.toFloat() / analysisH)
        val scaleForFill = maxOf(viewW.toFloat() / analysisW, viewH.toFloat() / analysisH)
        val scale = when (scaleType) {
            PreviewView.ScaleType.FIT_CENTER,
            PreviewView.ScaleType.FIT_START,
            PreviewView.ScaleType.FIT_END -> scaleForFit
            PreviewView.ScaleType.FILL_CENTER,
            PreviewView.ScaleType.FILL_START,
            PreviewView.ScaleType.FILL_END -> scaleForFill
        }
        val contentW = analysisW * scale
        val contentH = analysisH * scale
        val offsetX = when (scaleType) {
            PreviewView.ScaleType.FIT_START, PreviewView.ScaleType.FILL_START -> 0f
            PreviewView.ScaleType.FIT_CENTER, PreviewView.ScaleType.FILL_CENTER -> (viewW - contentW) / 2f
            PreviewView.ScaleType.FIT_END, PreviewView.ScaleType.FILL_END -> (viewW - contentW)
        }
        val offsetY = when (scaleType) {
            PreviewView.ScaleType.FIT_START, PreviewView.ScaleType.FILL_START -> 0f
            PreviewView.ScaleType.FIT_CENTER, PreviewView.ScaleType.FILL_CENTER -> (viewH - contentH) / 2f
            PreviewView.ScaleType.FIT_END, PreviewView.ScaleType.FILL_END -> (viewH - contentH)
        }
        val m = Matrix()
        if (mirror) {
            // Mirror around analysis buffer's vertical edge
            m.preTranslate(analysisW.toFloat(), 0f)
            m.preScale(-1f, 1f)
        }
        m.postScale(scale, scale)
        m.postTranslate(offsetX, offsetY)
        return m
    }

    private fun chooseAspectRatio(w: Int, h: Int): Int {
        val previewRatio = max(w, h).toDouble() / min(w, h)
        val diff4by3 = abs(previewRatio - 4.0 / 3.0)
        val diff16by9 = abs(previewRatio - 16.0 / 9.0)
        return if (diff4by3 <= diff16by9) AspectRatio.RATIO_4_3 else AspectRatio.RATIO_16_9
    }

    private fun registerCurrent() {
        val nv21 = lastNV21 ?: return
        val boxes = detectNV21(nv21, frameW, frameH, frameRotation,this@CameraActivity)
        if (boxes.isEmpty()) { Toast.makeText(this, "未检测到人脸", Toast.LENGTH_SHORT).show(); return }
        // pick largest
        var bi=0; var ba=0
        for (i in 0 until (boxes.size/4)) { val w=boxes[i*4+2]; val h=boxes[i*4+3]; if (w*h>ba){ba=w*h;bi=i} }
        val x=boxes[bi*4]; val y=boxes[bi*4+1]; val w=boxes[bi*4+2]; val h=boxes[bi*4+3]
        val feat = extractNV21(nv21, frameW, frameH, x,y,w,h, frameRotation,this@CameraActivity)
        if (feat.isEmpty()) { Toast.makeText(this, "提取特征失败", Toast.LENGTH_SHORT).show(); return }
        val name = nameEdit.text?.toString()?.ifBlank { null } ?: "person_${db.size+1}"
        db.add(Entry(name, feat))
        Toast.makeText(this, "已注册: $name", Toast.LENGTH_SHORT).show()
    }

    private fun cosine(a: FloatArray, b: FloatArray): Float {
        if (a.size != b.size) return -1f
        var dot=0.0; var na=0.0; var nb=0.0
        for (i in a.indices){ dot += a[i]*b[i]; na += a[i]*a[i]; nb += b[i]*b[i] }
        if (na==0.0 || nb==0.0) return -1f
        return (dot / (Math.sqrt(na)*Math.sqrt(nb))).toFloat()
    }

    private fun yuv420888ToNV21(image: ImageProxy): ByteArray {
        val w = image.width
        val h = image.height
        val nv21 = ByteArray(w * h * 3 / 2)

        val yPlane = image.planes[0]
        val uPlane = image.planes[1]
        val vPlane = image.planes[2]

        // Copy Y plane
        val yBuf = yPlane.buffer
        yBuf.rewind()
        val yRowStride = yPlane.rowStride
        val yPixelStride = yPlane.pixelStride
        var out = 0
        if (yPixelStride == 1 && yRowStride == w) {
            yBuf.get(nv21, 0, w * h)
            out = w * h
        } else {
            val yArr = ByteArray(yBuf.remaining())
            yBuf.get(yArr)
            for (row in 0 until h) {
                var col = 0
                val rowStart = row * yRowStride
                while (col < w) {
                    nv21[out++] = yArr[rowStart + col * yPixelStride]
                    col++
                }
            }
        }

        // Interleave VU for NV21
        val uBuf = uPlane.buffer
        val vBuf = vPlane.buffer
        uBuf.rewind(); vBuf.rewind()
        val uArr = ByteArray(uBuf.remaining())
        val vArr = ByteArray(vBuf.remaining())
        uBuf.get(uArr); vBuf.get(vArr)
        val uRowStride = uPlane.rowStride
        val vRowStride = vPlane.rowStride
        val uPixelStride = uPlane.pixelStride
        val vPixelStride = vPlane.pixelStride
        val uvWidth = w / 2
        val uvHeight = h / 2
        var uvOut = w * h
        for (row in 0 until uvHeight) {
            val uRowStart = row * uRowStride
            val vRowStart = row * vRowStride
            for (col in 0 until uvWidth) {
                val uIndex = uRowStart + col * uPixelStride
                val vIndex = vRowStart + col * vPixelStride
                nv21[uvOut++] = vArr.getOrElse(vIndex) { 0 }
                nv21[uvOut++] = uArr.getOrElse(uIndex) { 0 }
            }
        }

        return nv21
    }
}
