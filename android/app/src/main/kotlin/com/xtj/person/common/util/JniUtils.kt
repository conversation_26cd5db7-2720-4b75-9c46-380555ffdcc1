package com.xtj.person.common.util

import android.content.Context
import android.content.res.AssetManager
import android.graphics.Bitmap

object JniUtils {

    external fun detectBitmap(bitmap: Bitmap,context: Context): IntArray
    external fun init(assetManager: AssetManager, context: Context): Int

    external fun detectNV21(
        nv21: ByteArray,
        w: Int,
        h: Int,
        rotation: Int,
        context: Context
    ): IntArray

    external fun extractNV21(
        nv21: ByteArray,
        w: Int,
        h: Int,
        x: Int,
        y: Int,
        ww: Int,
        hh: Int,
        rotation: Int, context: Context
    ): FloatArray

    external fun seetaSimilarity(a: FloatArray, b: FloatArray, context: Context): Float
}