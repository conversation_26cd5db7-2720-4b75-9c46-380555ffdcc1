#include <jni.h>
#include <android/asset_manager.h>
#include <android/asset_manager_jni.h>
#include <android/bitmap.h>
#include <vector>
#include <string>
#include <cstring>
#include <mutex>
#include <android/log.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <errno.h>

#include <seeta/Common/Struct.h>
#include <seeta/Common/CStruct.h>
#include <seeta/FaceDetector.h>
#include <seeta/FaceLandmarker.h>
#include <seeta/FaceRecognizer.h>

#ifdef WITH_OPENCV
#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>
#endif

static seeta::FaceDetector* g_detector = nullptr;
static seeta::FaceLandmarker* g_landmarker = nullptr;
static seeta::FaceRecognizer* g_recognizer = nullptr;
static std::mutex g_model_mutex;
static bool g_models_ready = false;

#define LOG_TAG "SEETA_NATIVE"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

static std::vector<uint8_t> read_asset(AAssetManager* mgr, const char* path) {
    std::vector<uint8_t> data;
    AAsset* asset = AAssetManager_open(mgr, path, AASSET_MODE_BUFFER);
    if (!asset) return data;
    off_t len = AAsset_getLength(asset);
    data.resize(static_cast<size_t>(len));
    int64_t readn = AAsset_read(asset, data.data(), len);
    (void)readn;
    AAsset_close(asset);
    return data;
}

static void argb_to_bgr(uint8_t* dst_bgr, const uint8_t* src_argb, int w, int h, int stride) {
    for (int y = 0; y < h; ++y) {
        const uint8_t* row = src_argb + y * stride;
        uint8_t* out = dst_bgr + y * w * 3;
        for (int x = 0; x < w; ++x) {
            uint8_t a = row[4*x + 0];
            uint8_t r = row[4*x + 1];
            uint8_t g = row[4*x + 2];
            uint8_t b = row[4*x + 3];
            (void)a;
            out[3*x + 0] = b;
            out[3*x + 1] = g;
            out[3*x + 2] = r;
        }
    }
}

static void bgr_resize_nearest(const uint8_t* src_bgr, int sw, int sh,
                               uint8_t* dst_bgr, int dw, int dh) {
    // Simple nearest-neighbor for robustness and low overhead
    const double sx = static_cast<double>(sw) / dw;
    const double sy = static_cast<double>(sh) / dh;
    for (int y = 0; y < dh; ++y) {
        int syi = static_cast<int>(y * sy);
        if (syi >= sh) syi = sh - 1;
        for (int x = 0; x < dw; ++x) {
            int sxi = static_cast<int>(x * sx);
            if (sxi >= sw) sxi = sw - 1;
            const uint8_t* sp = src_bgr + (syi * sw + sxi) * 3;
            uint8_t* dp = dst_bgr + (y * dw + x) * 3;
            dp[0] = sp[0]; dp[1] = sp[1]; dp[2] = sp[2];
        }
    }
}

static std::string get_cache_dir(JNIEnv* env, jobject context) {
    jclass cls = env->GetObjectClass(context);
    jmethodID mid_cache = env->GetMethodID(cls, "getCacheDir", "()Ljava/io/File;");
    jobject fileObj = env->CallObjectMethod(context, mid_cache);
    jclass fileCls = env->GetObjectClass(fileObj);
    jmethodID mid_path = env->GetMethodID(fileCls, "getAbsolutePath", "()Ljava/lang/String;");
    jstring jpath = (jstring)env->CallObjectMethod(fileObj, mid_path);
    const char* cpath = env->GetStringUTFChars(jpath, nullptr);
    std::string path(cpath);
    env->ReleaseStringUTFChars(jpath, cpath);
    env->DeleteLocalRef(jpath);
    env->DeleteLocalRef(fileObj);
    return path;
}

static bool ensure_dirs(const std::string& dir) {
    if (dir.empty()) return true;
    // Iterate path segments and mkdir progressively
    std::string cur;
    cur.reserve(dir.size());
    for (size_t i = 0; i < dir.size(); ++i) {
        char c = dir[i];
        cur.push_back(c);
        if (c == '/' || i == dir.size() - 1) {
            // Skip root-only
            if (cur == "/" || cur.empty()) continue;
            struct stat st{};
            if (stat(cur.c_str(), &st) == 0) {
                if (!S_ISDIR(st.st_mode)) return false; // exists but not dir
            } else {
                if (mkdir(cur.c_str(), 0700) != 0) {
                    if (errno == EEXIST) continue;
                    return false;
                }
            }
        }
    }
    return true;
}

static bool write_file(const std::string& path, const uint8_t* data, size_t n) {
    auto slash = path.find_last_of('/');
    if (slash != std::string::npos) {
        std::string dir = path.substr(0, slash);
        if (!ensure_dirs(dir)) return false;
    }
    FILE* f = fopen(path.c_str(), "wb");
    if (!f) return false;
    fwrite(data, 1, n, f);
    fclose(f);
    return true;
}

extern "C" JNIEXPORT jint JNICALL
Java_com_xtj_person_common_util_JniUtils_init(JNIEnv* env, jobject  thiz, jobject assetManager, jobject context) {
    LOGI("init() called");
    std::lock_guard<std::mutex> lock(g_model_mutex);
    if (g_models_ready) return 0;
    
    auto* mgr = AAssetManager_fromJava(env, assetManager);
    if (!mgr) return -1;

    // Load models from assets and write to cache for path-based loading
    auto fd_bytes = read_asset(mgr, "models/face_detector.csta");
    auto lm_bytes = read_asset(mgr, "models/face_landmarker_pts5.csta");
    auto fr_bytes = read_asset(mgr, "models/face_recognizer.csta");
    if (fd_bytes.empty() || lm_bytes.empty() || fr_bytes.empty()) return -2;

    std::string base = get_cache_dir(env, context) + "/seeta_models";
    std::string fd_path = base + "/face_detector.csta";
    std::string lm_path = base + "/face_landmarker_pts5.csta";
    std::string fr_path = base + "/face_recognizer.csta";
    if (!write_file(fd_path, fd_bytes.data(), fd_bytes.size())) return -3;
    if (!write_file(lm_path, lm_bytes.data(), lm_bytes.size())) return -3;
    if (!write_file(fr_path, fr_bytes.data(), fr_bytes.size())) return -3;

    try {
        seeta::ModelSetting fd_setting(std::vector<std::string>{fd_path}, seeta::ModelSetting::CPU);
        seeta::ModelSetting lm_setting(std::vector<std::string>{lm_path}, seeta::ModelSetting::CPU);
        seeta::ModelSetting fr_setting(std::vector<std::string>{fr_path}, seeta::ModelSetting::CPU);
        
        // Clean up any existing models first
        if (g_detector) { delete g_detector; g_detector = nullptr; }
        if (g_landmarker) { delete g_landmarker; g_landmarker = nullptr; }
        if (g_recognizer) { delete g_recognizer; g_recognizer = nullptr; }
        
        g_detector = new seeta::FaceDetector(fd_setting);
        g_landmarker = new seeta::FaceLandmarker(lm_setting);
        g_recognizer = new seeta::FaceRecognizer(fr_setting);

        // Safer default: single thread to avoid vendor-specific crashes
        try {
            g_detector->set(seeta::FaceDetector::PROPERTY_ARM_CPU_MODE, 0);
            g_detector->set(seeta::FaceDetector::PROPERTY_NUMBER_THREADS, 1);
        } catch (...) {}
        // Cap internal processing size if supported by the library implementation
        try { g_detector->set(seeta::FaceDetector::PROPERTY_MAX_IMAGE_WIDTH, 4096); } catch (...) {}
        try { g_detector->set(seeta::FaceDetector::PROPERTY_MAX_IMAGE_HEIGHT, 4096); } catch (...) {}
        // Optional: avoid tiny faces which are noisy on high-res photos
        try {
            g_detector->set(seeta::FaceDetector::PROPERTY_MIN_FACE_SIZE, 40.0);
        } catch (...) {}
        
        g_models_ready = true;
        LOGI("models loaded OK");
    } catch (...) {
        LOGE("models load failed");
        // Clean up on failure
        if (g_detector) { delete g_detector; g_detector = nullptr; }
        if (g_landmarker) { delete g_landmarker; g_landmarker = nullptr; }
        if (g_recognizer) { delete g_recognizer; g_recognizer = nullptr; }
        g_models_ready = false;
        return -4;
    }
    return 0;
}

extern "C" JNIEXPORT jintArray JNICALL
Java_com_xtj_person_common_util_JniUtils_detectBitmap(JNIEnv* env, jobject thiz, jobject bitmap, jobject context) {
    (void)context;
    LOGI("detectBitmap enter");
    AndroidBitmapInfo info{};
    if (AndroidBitmap_getInfo(env, bitmap, &info) != ANDROID_BITMAP_RESULT_SUCCESS) {
        jintArray empty = env->NewIntArray(0); return empty;
    }
    if (info.format != ANDROID_BITMAP_FORMAT_RGBA_8888) {
        // Only support ARGB_8888 for simplicity
        jintArray empty = env->NewIntArray(0); return empty;
    }
    void* pixels = nullptr;
    if (AndroidBitmap_lockPixels(env, bitmap, &pixels) != ANDROID_BITMAP_RESULT_SUCCESS || pixels == nullptr) {
        jintArray empty = env->NewIntArray(0); return empty;
    }

    std::vector<uint8_t> bgr(info.width * info.height * 3);
    argb_to_bgr(bgr.data(), static_cast<uint8_t*>(pixels), info.width, info.height, info.stride);
    AndroidBitmap_unlockPixels(env, bitmap);

    int rw = static_cast<int>(info.width);
    int rh = static_cast<int>(info.height);
    // Downscale very large images to improve stability (and speed)
    const int MAX_SIDE = 640; // further downscale for stability testing
    std::vector<uint8_t> bgr_small;
    if (rw > MAX_SIDE || rh > MAX_SIDE) {
        double scale = std::min(MAX_SIDE / static_cast<double>(rw), MAX_SIDE / static_cast<double>(rh));
        int dw = std::max(1, static_cast<int>(rw * scale + 0.5));
        int dh = std::max(1, static_cast<int>(rh * scale + 0.5));
        LOGI("detectBitmap downscale %dx%d -> %dx%d", rw, rh, dw, dh);
        bgr_small.resize(dw * dh * 3);
        bgr_resize_nearest(bgr.data(), rw, rh, bgr_small.data(), dw, dh);
        rw = dw; rh = dh;
    }

    // Safe alignment: pad to multiples of 32 to avoid SIMD tail issues in some kernels
    const int ALIGN = 32;
    const int pw = ((rw + ALIGN - 1) / ALIGN) * ALIGN;
    const int ph = ((rh + ALIGN - 1) / ALIGN) * ALIGN;
    std::vector<uint8_t> bgr_aligned;
    uint8_t* src_ptr = (bgr_small.empty() ? bgr.data() : bgr_small.data());
    if (pw != rw || ph != rh) {
        LOGI("detectBitmap pad to %dx%d (from %dx%d)", pw, ph, rw, rh);
        bgr_aligned.assign(pw * ph * 3, 0);
        for (int y = 0; y < rh; ++y) {
            const uint8_t* srow = src_ptr + y * rw * 3;
            uint8_t* drow = bgr_aligned.data() + y * pw * 3;
            memcpy(drow, srow, static_cast<size_t>(rw) * 3);
        }
    }

    SeetaImageData simg{ (pw != rw || ph != rh) ? pw : rw,
                         (pw != rw || ph != rh) ? ph : rh,
                         3,
                         (bgr_aligned.empty() ? src_ptr : bgr_aligned.data()) };
    LOGI("detectBitmap prepared BGR rw=%d rh=%d (padded=%d)", simg.width, simg.height, (pw!=rw||ph!=rh));

    std::vector<jint> buf;
    {
        std::lock_guard<std::mutex> lock(g_model_mutex);
        if (!g_models_ready || !g_detector) {
            jintArray empty = env->NewIntArray(0); return empty;
        }

        auto faces = g_detector->detect(simg);
        buf.resize(faces.size * 4);
        for (int i = 0; i < faces.size; ++i) {
            auto r = faces.data[i].pos;
            buf[4*i+0] = r.x; buf[4*i+1] = r.y; buf[4*i+2] = r.width; buf[4*i+3] = r.height;
        }
    }
    jintArray out = env->NewIntArray(static_cast<jsize>(buf.size()));
    if (!buf.empty()) env->SetIntArrayRegion(out, 0, static_cast<jsize>(buf.size()), buf.data());
    return out;
}
static inline uint8_t clamp8(int v){ return (uint8_t)(v < 0 ? 0 : (v > 255 ? 255 : v)); }

#ifndef WITH_OPENCV
// Fallback: NV21 -> RGB & rotations in pure C
static void nv21_to_rgb(uint8_t* dst_rgb, const uint8_t* nv21, int w, int h) {
    const uint8_t* y_plane = nv21;
    const uint8_t* vu_plane = nv21 + w * h;
    for (int j = 0; j < h; ++j) {
        const uint8_t* yrow = y_plane + j * w;
        const uint8_t* uvrow = vu_plane + (j/2) * w;
        for (int i = 0; i < w; ++i) {
            int Y = (int)yrow[i] & 0xff;
            int V = (int)uvrow[(i/2)*2] & 0xff;
            int U = (int)uvrow[(i/2)*2 + 1] & 0xff;
            int C = Y - 16; if (C < 0) C = 0;
            int D = U - 128;
            int E = V - 128;
            int R = (298*C + 409*E + 128) >> 8;
            int G = (298*C - 100*D - 208*E + 128) >> 8;
            int B = (298*C + 516*D + 128) >> 8;
            dst_rgb[(j*w + i)*3 + 0] = clamp8(R);
            dst_rgb[(j*w + i)*3 + 1] = clamp8(G);
            dst_rgb[(j*w + i)*3 + 2] = clamp8(B);
        }
    }
}
static void rotate_rgb_90(uint8_t* dst, const uint8_t* src, int w, int h) {
    for (int y = 0; y < h; ++y)
        for (int x = 0; x < w; ++x) {
            const uint8_t* sp = src + (y*w + x)*3;
            int dx = h - 1 - y, dy = x; // dst is h x w
            uint8_t* dp = dst + (dy*h + dx)*3;
            dp[0]=sp[0]; dp[1]=sp[1]; dp[2]=sp[2];
        }
}
static void rotate_rgb_180(uint8_t* dst, const uint8_t* src, int w, int h) {
    for (int y = 0; y < h; ++y)
        for (int x = 0; x < w; ++x) {
            const uint8_t* sp = src + (y*w + x)*3;
            uint8_t* dp = dst + ((h-1-y)*w + (w-1-x))*3;
            dp[0]=sp[0]; dp[1]=sp[1]; dp[2]=sp[2];
        }
}
static void rotate_rgb_270(uint8_t* dst, const uint8_t* src, int w, int h) {
    for (int y = 0; y < h; ++y)
        for (int x = 0; x < w; ++x) {
            const uint8_t* sp = src + (y*w + x)*3;
            int dx = y, dy = w - 1 - x; // dst is h x w
            uint8_t* dp = dst + (dy*h + dx)*3;
            dp[0]=sp[0]; dp[1]=sp[1]; dp[2]=sp[2];
        }
}
#endif

extern "C" JNIEXPORT jintArray JNICALL
Java_com_xtj_person_common_util_JniUtils_detectNV21(JNIEnv* env, jobject  thiz, jbyteArray nv21, jint width, jint height, jint rotation, jobject context) {
    (void)context;
    LOGI("detectNV21 enter w=%d h=%d rot=%d", width, height, rotation);
    
    // Thread-safe check for model readiness
    {
        std::lock_guard<std::mutex> lock(g_model_mutex);
        if (!g_models_ready || !g_detector) {
            jintArray e = env->NewIntArray(0); 
            return e; 
        }
    }
    
    const int w = width, h = height;
    jsize n = env->GetArrayLength(nv21);
    
    // Validate input parameters
    if (n < w * h * 3 / 2 || w <= 0 || h <= 0) {
        jintArray e = env->NewIntArray(0);
        return e;
    }
    
    // Use aligned memory allocation
    std::vector<uint8_t> yuv(n);
    env->GetByteArrayRegion(nv21, 0, n, reinterpret_cast<jbyte*>(yuv.data()));
    LOGI("detectNV21 copied YUV len=%d", (int)n);
    
    // Check for errors in getting array region
    if (env->ExceptionCheck()) {
        env->ExceptionClear();
        jintArray e = env->NewIntArray(0);
        return e;
    }
    
    int rw=w, rh=h;
#ifdef WITH_OPENCV
    // Build separate plane views for robustness
    uint8_t* baseY = yuv.data();
    uint8_t* baseUV = yuv.data() + w * h;
    cv::Mat Y(h, w, CV_8UC1, baseY, (size_t)w);
    cv::Mat VU(h/2, w/2, CV_8UC2, baseUV, (size_t)w);
    cv::Mat bgr;
    cv::cvtColorTwoPlane(Y, VU, bgr, cv::COLOR_YUV2BGR_NV21);
    if (rotation == 90) { cv::rotate(bgr, bgr, cv::ROTATE_90_CLOCKWISE); rw=h; rh=w; }
    else if (rotation == 180) { cv::rotate(bgr, bgr, cv::ROTATE_180); }
    else if (rotation == 270) { cv::rotate(bgr, bgr, cv::ROTATE_90_COUNTERCLOCKWISE); rw=h; rh=w; }
    if (!bgr.isContinuous()) bgr = bgr.clone();
#else
    std::vector<uint8_t> frame;
    std::vector<uint8_t> rgb(w*h*3);
    nv21_to_rgb(rgb.data(), yuv.data(), w, h);
    std::vector<uint8_t> rgb_rot;
    if (rotation==90 || rotation==270) { rgb_rot.resize(w*h*3); }
    if (rotation==90) { rotate_rgb_90(rgb_rot.data(), rgb.data(), w, h); rw=h; rh=w; }
    else if (rotation==180) { rgb_rot.resize(w*h*3); rotate_rgb_180(rgb_rot.data(), rgb.data(), w, h); }
    else if (rotation==270) { rotate_rgb_270(rgb_rot.data(), rgb.data(), w, h); rw=h; rh=w; }
    frame.assign((rotation==0)? rgb.begin(): rgb_rot.begin(), (rotation==0)? rgb.end(): rgb_rot.end());
#endif
    LOGI("detectNV21 prepared BGR rw=%d rh=%d", rw, rh);
    
    // Ensure valid data pointer
    #ifndef WITH_OPENCV
    if (frame.empty()) {
        jintArray e = env->NewIntArray(0);
        return e;
    }
    #endif
    
    // Optionally downscale very large frames to improve stability on newer Android versions
    const int MAX_SIDE_DET = 960; // more aggressive downscale for detection
    #ifdef WITH_OPENCV
    if (rw > MAX_SIDE_DET || rh > MAX_SIDE_DET) {
        double scale = std::min(MAX_SIDE_DET / static_cast<double>(rw), MAX_SIDE_DET / static_cast<double>(rh));
        int dw = std::max(1, static_cast<int>(rw * scale + 0.5));
        int dh = std::max(1, static_cast<int>(rh * scale + 0.5));
        LOGI("detectNV21 downscale %dx%d -> %dx%d", rw, rh, dw, dh);
        cv::Mat bgr_small;
        cv::resize(bgr, bgr_small, cv::Size(dw, dh), 0, 0, cv::INTER_AREA);
        bgr = bgr_small;
        rw = dw; rh = dh;
    }
    SeetaImageData simg{rw, rh, 3, bgr.data};
    #else
    std::vector<uint8_t> frame_small;
    if (rw > MAX_SIDE_DET || rh > MAX_SIDE_DET) {
        double scale = std::min(MAX_SIDE_DET / static_cast<double>(rw), MAX_SIDE_DET / static_cast<double>(rh));
        int dw = std::max(1, static_cast<int>(rw * scale + 0.5));
        int dh = std::max(1, static_cast<int>(rh * scale + 0.5));
        LOGI("detectNV21 downscale %dx%d -> %dx%d", rw, rh, dw, dh);
        frame_small.resize(dw * dh * 3);
        bgr_resize_nearest(frame.data(), rw, rh, frame_small.data(), dw, dh);
        frame.swap(frame_small);
        rw = dw; rh = dh;
    }
    SeetaImageData simg{rw, rh, 3, frame.data()};
    #endif
    LOGI("detectNV21 calling detector:%x", g_detector);

    std::vector<jint> buf;
    {
        std::lock_guard<std::mutex> lock(g_model_mutex);
        if (!g_models_ready || !g_detector) {
            jintArray e = env->NewIntArray(0);
            return e;
        }
        auto faces = g_detector->detect(simg);
        LOGI("detectNV21 faces=%d", faces.size);
        buf.resize(faces.size * 4);
        for (int i=0;i<faces.size;++i){ 
            auto r=faces.data[i].pos; 
            buf[4*i+0]=r.x; buf[4*i+1]=r.y; buf[4*i+2]=r.width; buf[4*i+3]=r.height; 
        }
    }
    jintArray out = env->NewIntArray(static_cast<jsize>(buf.size()));
    if (!buf.empty()) env->SetIntArrayRegion(out, 0, (jsize)buf.size(), buf.data());
    return out;
}

extern "C" JNIEXPORT jfloatArray JNICALL
Java_com_xtj_person_common_util_JniUtils_extractNV21(JNIEnv* env, jobject  thiz, jbyteArray nv21, jint width, jint height, jint x, jint y, jint w, jint h, jint rotation, jobject context) {
    (void)context;
    LOGI("extractNV21 enter w=%d h=%d box=(%d,%d,%d,%d) rot=%d", width, height, x, y, w, h, rotation);
    
    // Thread-safe check for model readiness
    {
        std::lock_guard<std::mutex> lock(g_model_mutex);
        if (!g_models_ready || !g_landmarker || !g_recognizer || !g_detector) { 
            return env->NewFloatArray(0); 
        }
    }
    
    jsize n = env->GetArrayLength(nv21);
    
    // Validate input parameters
    if (n < width * height * 3 / 2 || width <= 0 || height <= 0 || w <= 0 || h <= 0) {
        return env->NewFloatArray(0);
    }
    
    std::vector<uint8_t> yuv(n);
    env->GetByteArrayRegion(nv21, 0, n, reinterpret_cast<jbyte*>(yuv.data()));
    LOGI("extractNV21 copied YUV len=%d", (int)n);
    
    // Check for errors in getting array region
    if (env->ExceptionCheck()) {
        env->ExceptionClear();
        return env->NewFloatArray(0);
    }
    
    int rw=width, rh=height;
#ifdef WITH_OPENCV
    // ROI-only conversion: map rotated box back to original NV21 coords
    auto align_even = [](int v){ return (v & ~1); };
    int x0=x, y0=y, w0=w, h0=h;
    // Inverse-rotate rect (x,y,w,h) from rotated space to original NV21 space
    if (rotation == 90) {
        // inverse: x = y', y = H-1 - x'
        x0 = y;
        y0 = height - x - w;
        w0 = h;
        h0 = w;
    } else if (rotation == 180) {
        x0 = width - x - w;
        y0 = height - y - h;
        w0 = w;
        h0 = h;
    } else if (rotation == 270) {
        // inverse: x = W-1 - y', y = x'
        x0 = width - y - h;
        y0 = x;
        w0 = h;
        h0 = w;
    } else {
        x0 = x; y0 = y; w0 = w; h0 = h;
    }
    // Clip to image bounds
    if (x0 < 0) { w0 += x0; x0 = 0; }
    if (y0 < 0) { h0 += y0; y0 = 0; }
    if (x0 + w0 > width)  w0 = width  - x0;
    if (y0 + h0 > height) h0 = height - y0;
    if (w0 <= 0 || h0 <= 0) {
        return env->NewFloatArray(0);
    }
    // Align to even for NV21 (2x2 chroma)
    int xe = align_even(x0);
    int ye = align_even(y0);
    int we = align_even(w0);
    int he = align_even(h0);
    // Ensure still valid after alignment
    if (xe + we > width)  we = align_even(width - xe);
    if (ye + he > height) he = align_even(height - ye);
    if (we <= 0 || he <= 0) {
        return env->NewFloatArray(0);
    }

    // Build plane views without copy (Y: HxW, UV: H/2 x W/2 with 2 channels)
    uint8_t* baseY = yuv.data();
    uint8_t* baseUV = yuv.data() + width * height;
    cv::Mat Y(height, width, CV_8UC1, baseY, (size_t)width);
    // UV plane as 2-channel, step equals original width in bytes
    cv::Mat UV(height/2, width/2, CV_8UC2, baseUV, (size_t)width);
    cv::Rect yrect(xe, ye, we, he);
    // UV coords/size are halved
    cv::Rect uvrect(xe/2, ye/2, we/2, he/2);
    cv::Mat y_roi = Y(yrect);
    cv::Mat uv_roi = UV(uvrect);

    // Convert only ROI to BGR
    cv::Mat bgr2;
    cv::cvtColorTwoPlane(y_roi, uv_roi, bgr2, cv::COLOR_YUV2BGR_NV21);
    // Rotate ROI to the same orientation used by detection, so rect becomes full ROI
    if (rotation == 90) { cv::rotate(bgr2, bgr2, cv::ROTATE_90_CLOCKWISE); }
    else if (rotation == 180) { cv::rotate(bgr2, bgr2, cv::ROTATE_180); }
    else if (rotation == 270) { cv::rotate(bgr2, bgr2, cv::ROTATE_90_COUNTERCLOCKWISE); }
    if (!bgr2.isContinuous()) bgr2 = bgr2.clone();
#else
    std::vector<uint8_t> frame2;
    std::vector<uint8_t> rgb(width*height*3);
    nv21_to_rgb(rgb.data(), yuv.data(), width, height);
    std::vector<uint8_t> rgb_rot; 
    if (rotation==90 || rotation==270) { rgb_rot.resize(width*height*3); }
    if (rotation==90) { rotate_rgb_90(rgb_rot.data(), rgb.data(), width, height); rw=height; rh=width; }
    else if (rotation==180) { rgb_rot.resize(width*height*3); rotate_rgb_180(rgb_rot.data(), rgb.data(), width, height); }
    else if (rotation==270) { rotate_rgb_270(rgb_rot.data(), rgb.data(), width, height); rw=height; rh=width; }
    frame2.assign((rotation==0)? rgb.begin(): rgb_rot.begin(), (rotation==0)? rgb.end(): rgb_rot.end());
#endif
    #ifndef WITH_OPENCV
    if (frame2.empty()) { return env->NewFloatArray(0); }
    #else
    // For ROI path, set rect as full image
    rw = bgr2.cols;
    rh = bgr2.rows;
    #endif
    LOGI("extractNV21 prepared BGR rw=%d rh=%d", rw, rh);

    #ifdef WITH_OPENCV
    SeetaImageData simg{rw, rh, 3, bgr2.data};
    #else
    SeetaImageData simg{rw, rh, 3, frame2.data()};
    #endif
    // clip rect into bounds
    auto clip = [&](int& xx, int& yy, int& ww, int& hh)->bool{
        if (rw <= 1 || rh <= 1) return false;
        if (ww <= 0 || hh <= 0) return false;
        if (xx < 0) { ww += xx; xx = 0; }
        if (yy < 0) { hh += yy; yy = 0; }
        if (xx >= rw || yy >= rh) return false;
        if (xx + ww > rw) ww = rw - xx;
        if (yy + hh > rh) hh = rh - yy;
        if (ww < 2 || hh < 2) return false;
        return true;
    };
    int rx = 0, ry = 0, rw0 = rw, rh0 = rh;
    if (!clip(rx, ry, rw0, rh0)) {
        LOGE("extractNV21 clip failed");
        return env->NewFloatArray(0);
    }
    LOGI("extractNV21 rect clipped=(%d,%d,%d,%d)", rx, ry, rw0, rh0);
    SeetaRect rect{rx, ry, rw0, rh0};
    
    // Thread-safe operations
    SeetaPointF points[5];
    int dim;
    std::vector<float> feat;
    
    {
        std::lock_guard<std::mutex> lock(g_model_mutex);
        if (!g_models_ready || !g_landmarker || !g_recognizer) {
            return env->NewFloatArray(0);
        }
        
        auto point_array = g_landmarker->mark(simg, rect);
        LOGI("extractNV21 landmarks=%d", (int)point_array.size());
        if (point_array.size() < 5) {
            LOGE("extractNV21 not enough landmarks");
            return env->NewFloatArray(0);
        }
        for (int i = 0; i < 5; ++i) {
            points[i] = point_array[i];
        }
        
        dim = g_recognizer->GetExtractFeatureSize();
        feat.resize(dim);
        LOGI("extractNV21 calling recognizer dim=%d", dim);
        g_recognizer->Extract(simg, points, feat.data());
    }
    LOGI("extractNV21 done");
    
    jfloatArray out = env->NewFloatArray(dim);
    env->SetFloatArrayRegion(out, 0, dim, feat.data());
    return out;
}

extern "C" JNIEXPORT jfloat JNICALL
Java_com_xtj_person_common_util_JniUtils_seetaSimilarity(JNIEnv* env, jobject  thiz, jfloatArray a, jfloatArray b,jobject context) {
    (void)context;
    std::lock_guard<std::mutex> lock(g_model_mutex);
    if (!g_models_ready || !g_recognizer) return -1.0f;
    if (a == nullptr || b == nullptr) return -1.0f;
    jsize na = env->GetArrayLength(a);
    jsize nb = env->GetArrayLength(b);
    int dim = g_recognizer->GetExtractFeatureSize();
    if (na <= 0 || nb <= 0 || na != nb || (dim > 0 && na != dim)) return -1.0f;
    std::vector<float> va(na), vb(nb);
    env->GetFloatArrayRegion(a, 0, na, va.data());
    if (env->ExceptionCheck()) { env->ExceptionClear(); return -1.0f; }
    env->GetFloatArrayRegion(b, 0, nb, vb.data());
    if (env->ExceptionCheck()) { env->ExceptionClear(); return -1.0f; }
    float s = -1.0f;
    try {
        s = g_recognizer->CalculateSimilarity(va.data(), vb.data());
    } catch (...) {
        s = -1.0f;
    }
    return s;
}
