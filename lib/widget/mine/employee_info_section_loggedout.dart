import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/custom_tips_dialog.dart';
import 'package:npemployee/common/dialog/register_dialog.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:tap_debouncer/tap_debouncer.dart';

class EmployeeInfoSectionLoggedout extends StatefulWidget {
  final String? avatarPath;
  final void Function()? onRegisterTap;
  final void Function()? onExitTap;
  final void Function()? onRefreshReviewStatus;
  final String reviewStatus;
  final Map? reviewer;
  const EmployeeInfoSectionLoggedout(
      {super.key,
      required this.onRegisterTap,
      required this.onExitTap,
      this.onRefreshReviewStatus,
      this.avatarPath,
      required this.reviewStatus,
      this.reviewer});

  @override
  State<EmployeeInfoSectionLoggedout> createState() =>
      _EmployeeInfoSectionLoggedoutState();
}

class _EmployeeInfoSectionLoggedoutState
    extends State<EmployeeInfoSectionLoggedout> {
  bool get isSystem => widget.reviewer?['guid'] == 'system_approval';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: TapDebouncer(onTap: () async {
        if (widget.reviewStatus != 'waiting_review') {
          showDialog(
              context: context,
              builder: (_) {
                return RegisterDialog(
                  onClick: (v) {
                    if (v == 'register') {
                      widget.onRegisterTap!();
                    } else if (v == 'exit') {
                      widget.onExitTap!();
                    }
                  },
                );
              });
        } else {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return CustomTipsDialog(
                canPop: true,
                imagePath: 'assets/png/commit_success.png',
                title: '信息提交成功\n请耐心等待审核',
                status: 'review_not_pass', //仅仅为了展示content，可以忽略
                content: '审核人：${widget.reviewer?['name']}',
                isSystem: isSystem,
                onPressed: () {
                  NavigatorUtils.pop(context);
                },
              );
            },
          );
        }
      }, builder: (_, TapDebouncerFunc? onTap) {
        return GestureDetector(
          onTap:
              onTap, // Trigger the login tap callback when the user clicks on this section
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: widget.avatarPath == null
                    ? Image.asset(
                        'assets/png/default_avatar.png', // Replace with your local PNG path
                        width: 60,
                        height: 60,
                        fit: BoxFit
                            .cover, // Ensures the image fits within the specified dimensions
                      )
                    : Image.network(widget.avatarPath!,
                        width: 60, height: 60, fit: BoxFit.cover),
              ),
              const SizedBox(width: 12),
              // "点击登录" text
              /*  if (islogin)
                Text(
                  islogin ? '立即注册' : '点击登录',
                  style: AppTheme.getTextStyle(
                          baseSize: 18, color: AppTheme.primaryColor)
                      .pfSemiBold,
                ), */
              Container(
                width: 240.w,
                constraints: const BoxConstraints(maxHeight: 60),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                        '当前登录手机号: ${GlobalPreferences().userLoginModel?.mobile}',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 15.sp)
                            .pfSemiBold),
                    SizedBox(height: 8.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          widget.reviewStatus == 'waiting_review'
                              ? "已申请，审批中"
                              : '点击申请注册新途径人',
                          style: TextStyle(
                                  color: AppTheme.colorBlue,
                                  fontSize: 12.sp,
                                  decoration: TextDecoration.underline,
                                  decorationColor: AppTheme.colorBlue)
                              .pfMedium,
                        ),
                        if (widget.reviewStatus == 'waiting_review')
                          GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              widget.onRefreshReviewStatus?.call();
                            },
                            child: Text(
                              "状态刷新",
                              style: TextStyle(
                                      color: AppTheme.colorBlue,
                                      fontSize: 12.sp,
                                      decoration: TextDecoration.underline,
                                      decorationColor: AppTheme.colorBlue)
                                  .pfMedium,
                            ),
                          ),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}





/* class EmployeeInfoSectionLoggedOut extends StatelessWidget {
  // final bool islogin;
  final String? avatarPath;
  final void Function()? onRegisterTap;
  final void Function()? onExitTap;
  final String reviewStatus;

  const EmployeeInfoSectionLoggedOut({
    required this.onRegisterTap,
    required this.onExitTap,
    this.avatarPath,
    required this.reviewStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: TapDebouncer(onTap: () async {
        if (reviewStatus != 'waiting_review') {
          showDialog(
              context: context,
              builder: (_) {
                return RegisterDialog(
                  onClick: (v) {
                    if (v == 'register') {
                      onRegisterTap!();
                    } else if (v == 'exit') {
                      onExitTap!();
                    }
                  },
                );
              });
        } else {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return CustomTipsDialog(
                imagePath: 'assets/png/commit_success.png',
                title: '信息提交成功\n请耐心等待审核',
                status: 'review_not_pass', //仅仅为了展示content，可以忽略
                content: '审核人：正北',
                buttonText: '我知道了',
                onPressed: () {
                  NavigatorUtils.pop(context);
                },
              );
            },
          );
        }
      }, builder: (_, TapDebouncerFunc? onTap) {
        return GestureDetector(
          onTap:
              onTap, // Trigger the login tap callback when the user clicks on this section
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: avatarPath == null
                    ? Image.asset(
                        'assets/png/default_avatar.png', // Replace with your local PNG path
                        width: 60,
                        height: 60,
                        fit: BoxFit
                            .cover, // Ensures the image fits within the specified dimensions
                      )
                    : Image.network(avatarPath!,
                        width: 60, height: 60, fit: BoxFit.cover),
              ),
              const SizedBox(width: 12),
              // "点击登录" text
              /*  if (islogin)
                Text(
                  islogin ? '立即注册' : '点击登录',
                  style: AppTheme.getTextStyle(
                          baseSize: 18, color: AppTheme.primaryColor)
                      .pfSemiBold,
                ), */
              Container(
                constraints: const BoxConstraints(maxHeight: 60),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                        '当前登录手机号: ${GlobalPreferences().userLoginModel?.mobile}',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 15.sp)
                            .pfSemiBold),
                    SizedBox(height: 8.h),
                    Text(
                      reviewStatus == 'waiting_review'
                          ? "已申请，审批中"
                          : '点击申请注册新途径人',
                      style: TextStyle(
                        color: AppTheme.colorBlue,
                        fontSize: 12.sp,
                        decoration: TextDecoration.underline,
                      ).pfMedium,
                    )
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
 */